# Quick Usage Guide 🚀

## Getting Started

Your Simple Chatbot is now ready to use! Here's how to get started:

### 1. Start the Chatbot

```bash
# Start with default model (GPT-2 Large)
python -m chatbot.cli chat

# Start with specific model
python -m chatbot.cli chat --model llama-3.2-1b

# Use verbose output for debugging
python -m chatbot.cli chat --verbose
```

### 2. Interactive Commands

Once in the chat interface, you can use these commands:

- **`/help`** - Show help message
- **`/models`** - List available models
- **`/switch <model>`** - Switch to different model
  - Example: `/switch llama-3.2-1b`
- **`/info`** - Show current model information and GPU usage
- **`/clear`** - Clear conversation history
- **`/save [filename]`** - Save current session
  - Example: `/save my_conversation`
- **`/load <filename>`** - Load saved session
- **`/exit`** - Exit the chat

### 3. Example Session

```
🤖 GPU-Accelerated CLI Chatbot

You: Hello! Can you help me write a Python function?
Assistant: Of course! I'd be happy to help you write a Python function. What kind of function are you looking to create?

You: /switch llama-3.2-1b
Switching to llama-3.2-1b...
✓ Switched to llama-3.2-1b

You: A function to calculate fibonacci numbers
Assistant: Here's a Python function to calculate Fibonacci numbers:

```python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)
```

You: /info
[Shows model information and GPU usage]

You: /save fibonacci_help
✓ Session saved to sessions/fibonacci_help.json

You: /exit
```

### 4. Available Models

- **gpt2-large** - OpenAI GPT-2 Large (774M parameters)
- **llama-3.2-1b** - Meta Llama 3.2 1B Instruct

### 5. Configuration

Edit `chatbot/config/models.yaml` to:
- Change default model
- Adjust generation parameters (temperature, max_tokens, etc.)
- Add new models

### 6. Troubleshooting

**GPU Memory Issues:**
```bash
# Check GPU usage
You: /info

# Clear GPU memory by restarting
# Or switch to a smaller model
You: /switch gpt2-large
```

**Model Download:**
- First time using a model will download it from Hugging Face
- GPT-2 Large: ~3GB download
- Llama 3.2 1B: ~2.5GB download

**Performance Tips:**
- Use quantization (enabled by default) for memory efficiency
- Close other GPU applications for better performance
- Monitor GPU memory with `/info` command

### 7. Session Management

Sessions are automatically saved to the `sessions/` directory:
```bash
# List saved sessions
ls sessions/

# Load a specific session
You: /load my_conversation
```

### 8. Adding New Models

To add a new model, edit `chatbot/config/models.yaml`:

```yaml
models:
  my-new-model:
    name: "My New Model"
    model_path: "huggingface/model-name"
    max_tokens: 512
    temperature: 0.7
    use_quantization: true
```

Then implement the model class in `chatbot/core/models/` if needed.

---

**Enjoy chatting with your AI assistant! 🤖✨**
