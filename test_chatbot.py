#!/usr/bin/env python3
"""
Test script to verify the chatbot functionality without starting the full interactive session.
This script tests model loading and basic response generation.
"""

import sys
import torch
from chatbot.config.config import ChatConfig
from chatbot.core.chatbot import ChatBot

def test_gpu_setup():
    """Test GPU setup"""
    print("🔧 Testing GPU Setup...")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        print(f"Current GPU: {torch.cuda.get_device_name()}")
    print()

def test_config_loading():
    """Test configuration loading"""
    print("📋 Testing Configuration Loading...")
    try:
        config = ChatConfig.load_config()
        print(f"✅ Configuration loaded successfully")
        print(f"Current model: {config.current_model}")
        print(f"Available models: {list(config.models.keys())}")
        print()
        return config
    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
        return None

def test_chatbot_initialization(config):
    """Test chatbot initialization"""
    print("🤖 Testing ChatBot Initialization...")
    try:
        chatbot = ChatBot(config)
        print("✅ ChatBot instance created successfully")
        print()
        return chatbot
    except Exception as e:
        print(f"❌ ChatBot initialization failed: {e}")
        return None

def test_model_loading(chatbot, model_name="gpt2-large"):
    """Test model loading"""
    print(f"📦 Testing Model Loading ({model_name})...")
    try:
        print("⏳ Loading model (this may take a few minutes for first-time download)...")
        chatbot.load_model(model_name)
        print(f"✅ Model {model_name} loaded successfully")
        
        # Get model info
        info = chatbot.get_model_info()
        print(f"Model name: {info.get('name', 'Unknown')}")
        print(f"Device: {info.get('device', 'Unknown')}")
        print(f"Parameters: {info.get('parameters', 0):,}")
        
        if torch.cuda.is_available():
            memory = info.get('memory_usage', {})
            if isinstance(memory, dict) and 'allocated' in memory:
                print(f"GPU Memory: {memory['allocated']:.2f} GB allocated")
        print()
        return True
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False

def test_response_generation(chatbot):
    """Test response generation"""
    print("💬 Testing Response Generation...")
    try:
        test_prompt = "Hello! How are you today?"
        print(f"User: {test_prompt}")
        
        response = chatbot.chat(test_prompt)
        print(f"Assistant: {response}")
        print("✅ Response generation successful")
        print()
        return True
    except Exception as e:
        print(f"❌ Response generation failed: {e}")
        return False

def test_model_switching(chatbot):
    """Test model switching"""
    print("🔄 Testing Model Switching...")
    try:
        available_models = chatbot.get_available_models()
        if len(available_models) > 1:
            # Switch to a different model
            current = chatbot.current_model_type
            other_model = [m for m in available_models if m != current][0]
            
            print(f"Switching from {current} to {other_model}...")
            chatbot.switch_model(other_model)
            print(f"✅ Successfully switched to {other_model}")
            
            # Test a quick response
            response = chatbot.chat("Hi there!")
            print(f"Response from {other_model}: {response[:100]}...")
            print()
            return True
        else:
            print("⚠️  Only one model available, skipping switch test")
            return True
    except Exception as e:
        print(f"❌ Model switching failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Simple Chatbot Test Suite")
    print("=" * 50)
    
    # Test GPU setup
    test_gpu_setup()
    
    # Test configuration
    config = test_config_loading()
    if not config:
        sys.exit(1)
    
    # Test chatbot initialization
    chatbot = test_chatbot_initialization(config)
    if not chatbot:
        sys.exit(1)
    
    # Test model loading
    if not test_model_loading(chatbot):
        sys.exit(1)
    
    # Test response generation
    if not test_response_generation(chatbot):
        sys.exit(1)
    
    # Test model switching (if multiple models available)
    if not test_model_switching(chatbot):
        sys.exit(1)
    
    # Cleanup
    print("🧹 Cleaning up...")
    chatbot.cleanup()
    
    print("🎉 All tests passed! Your chatbot is ready to use.")
    print("\nTo start the interactive chat:")
    print("python -m chatbot.cli chat")
    print("or")
    print("python -m chatbot.cli --model llama-3.2-1b")

if __name__ == "__main__":
    main()
